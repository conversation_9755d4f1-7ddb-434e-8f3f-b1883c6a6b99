package com.tinyzk.user.center.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tinyzk.user.center.entity.UserSkills;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户技能Mapper接口
 */
@Mapper
public interface UserSkillsMapper extends BaseMapper<UserSkills> {

    /**
     * 根据用户ID和技能名称查询技能
     *
     * @param userId 用户ID
     * @param skillName 技能名称
     * @return 技能信息
     */
    UserSkills selectByUserIdAndSkillName(@Param("userId") Long userId, @Param("skillName") String skillName);

    /**
     * 根据用户ID查询所有技能
     *
     * @param userId 用户ID
     * @return 技能列表
     */
    List<UserSkills> selectByUserId(@Param("userId") Long userId);

    /**
     * 批量插入技能
     *
     * @param skills 技能列表
     * @return 插入数量
     */
    int batchInsert(@Param("skills") List<UserSkills> skills);
}
