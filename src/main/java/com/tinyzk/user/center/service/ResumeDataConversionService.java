package com.tinyzk.user.center.service;

import com.tinyzk.user.center.dto.ThirdPartyParseResultDTO;
import com.tinyzk.user.center.entity.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 简历数据转换服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ResumeDataConversionService {

    private static final String DATA_SOURCE_RESUME = "resume";
    private static final String DEFAULT_VISIBILITY = "public";

    /**
     * 转换基本信息
     */
    public UserProfile convertBasicInfo(Long userId, ThirdPartyParseResultDTO.BasicInfo basicInfo) {
        if (basicInfo == null) {
            return null;
        }

        UserProfile profile = new UserProfile();
        profile.setUserId(userId);
        
        // 昵称
        if (StringUtils.hasText(basicInfo.getName())) {
            profile.setNickname(basicInfo.getName());
        }
        
        // 性别转换
        if (StringUtils.hasText(basicInfo.getGender())) {
            profile.setGender(convertGender(basicInfo.getGender()));
        }
        
        // 生日转换
        if (StringUtils.hasText(basicInfo.getDateOfBirth())) {
            profile.setBirthday(parseDate(basicInfo.getDateOfBirth()));
        }
        
        // 地区信息
        if (StringUtils.hasText(basicInfo.getCurrentLocation())) {
            profile.setRegionName(basicInfo.getCurrentLocation());
        }
        
        if (StringUtils.hasText(basicInfo.getDetailedLocation())) {
            profile.setAddress(basicInfo.getDetailedLocation());
        }
        
        // 个人简介 - 可以从期望职位和行业组合
        StringBuilder bio = new StringBuilder();
        if (StringUtils.hasText(basicInfo.getDesiredPosition())) {
            bio.append("期望职位: ").append(basicInfo.getDesiredPosition());
        }
        if (StringUtils.hasText(basicInfo.getDesiredIndustry())) {
            if (bio.length() > 0) bio.append("; ");
            bio.append("期望行业: ").append(basicInfo.getDesiredIndustry());
        }
        if (bio.length() > 0) {
            profile.setBio(bio.toString());
        }

        return profile;
    }

    /**
     * 转换联系方式
     */
    public List<UserContactMethods> convertContactInfo(Long userId, ThirdPartyParseResultDTO.ContactInfo contactInfo) {
        if (contactInfo == null) {
            return new ArrayList<>();
        }

        List<UserContactMethods> contacts = new ArrayList<>();

        // 手机号
        if (StringUtils.hasText(contactInfo.getPhoneNumber())) {
            UserContactMethods contact = new UserContactMethods();
            contact.setUserId(userId);
            contact.setContactType(2); // 工作电话
            contact.setContactValue(contactInfo.getPhoneNumber());
            contact.setLabel("手机号");
            contact.setVisibility(DEFAULT_VISIBILITY);
            contact.setIsVerified(false);
            contacts.add(contact);
        }

        // 家庭电话
        if (StringUtils.hasText(contactInfo.getHomePhoneNumber())) {
            UserContactMethods contact = new UserContactMethods();
            contact.setUserId(userId);
            contact.setContactType(2); // 工作电话
            contact.setContactValue(contactInfo.getHomePhoneNumber());
            contact.setLabel("家庭电话");
            contact.setVisibility(DEFAULT_VISIBILITY);
            contact.setIsVerified(false);
            contacts.add(contact);
        }

        // 邮箱
        if (StringUtils.hasText(contactInfo.getEmail())) {
            UserContactMethods contact = new UserContactMethods();
            contact.setUserId(userId);
            contact.setContactType(1); // 备用邮箱
            contact.setContactValue(contactInfo.getEmail());
            contact.setLabel("邮箱");
            contact.setVisibility(DEFAULT_VISIBILITY);
            contact.setIsVerified(false);
            contacts.add(contact);
        }

        // 微信
        if (StringUtils.hasText(contactInfo.getWechat())) {
            UserContactMethods contact = new UserContactMethods();
            contact.setUserId(userId);
            contact.setContactType(4); // 微信
            contact.setContactValue(contactInfo.getWechat());
            contact.setLabel("微信");
            contact.setVisibility(DEFAULT_VISIBILITY);
            contact.setIsVerified(false);
            contacts.add(contact);
        }

        // QQ
        if (StringUtils.hasText(contactInfo.getQq())) {
            UserContactMethods contact = new UserContactMethods();
            contact.setUserId(userId);
            contact.setContactType(5); // QQ
            contact.setContactValue(contactInfo.getQq());
            contact.setLabel("QQ");
            contact.setVisibility(DEFAULT_VISIBILITY);
            contact.setIsVerified(false);
            contacts.add(contact);
        }

        return contacts;
    }

    /**
     * 转换教育经历
     */
    public List<UserEducationHistory> convertEducationExperience(Long userId, 
            List<ThirdPartyParseResultDTO.EducationExperience> educationList) {
        if (educationList == null || educationList.isEmpty()) {
            return new ArrayList<>();
        }

        List<UserEducationHistory> educations = new ArrayList<>();
        
        for (ThirdPartyParseResultDTO.EducationExperience edu : educationList) {
            UserEducationHistory education = new UserEducationHistory();
            education.setUserId(userId);
            education.setSchoolName(edu.getSchoolName());
            education.setDegree(edu.getDegree());
            education.setMajor(edu.getMajor());
            
            // 转换学位等级
            education.setDegreeLevel(convertDegreeLevel(edu.getDegree()));
            
            // 转换日期
            education.setStartDate(parseYearMonth(edu.getStartTimeYear(), edu.getStartTimeMonth()));
            education.setEndDate(parseYearMonth(edu.getEndTimeYear(), edu.getEndTimeMonth()));
            
            // GPA
            if (StringUtils.hasText(edu.getGpa())) {
                try {
                    education.setMajorGpa(Float.parseFloat(edu.getGpa()));
                } catch (NumberFormatException e) {
                    log.warn("无法解析GPA: {}", edu.getGpa());
                }
            }
            
            // 描述信息
            StringBuilder desc = new StringBuilder();
            if (StringUtils.hasText(edu.getCourses())) {
                desc.append("主要课程: ").append(edu.getCourses());
            }
            if (StringUtils.hasText(edu.getRanking())) {
                if (desc.length() > 0) desc.append("; ");
                desc.append("排名: ").append(edu.getRanking());
            }
            if (desc.length() > 0) {
                education.setDescription(desc.toString());
            }
            
            education.setVisibility(DEFAULT_VISIBILITY);
            educations.add(education);
        }

        return educations;
    }

    /**
     * 转换工作经历
     */
    public List<UserWorkHistory> convertWorkExperience(Long userId, 
            List<ThirdPartyParseResultDTO.WorkExperience> workList) {
        if (workList == null || workList.isEmpty()) {
            return new ArrayList<>();
        }

        List<UserWorkHistory> workHistories = new ArrayList<>();
        
        for (ThirdPartyParseResultDTO.WorkExperience work : workList) {
            UserWorkHistory workHistory = new UserWorkHistory();
            workHistory.setUserId(userId);
            workHistory.setCompanyName(work.getCompanyName());
            workHistory.setPositionName(work.getJobTitle());
            workHistory.setDepartment(work.getDepartment());
            workHistory.setDescription(work.getDescription());
            
            // 转换日期
            workHistory.setStartDate(parseYearMonth(work.getStartTimeYear(), work.getStartTimeMonth()));
            workHistory.setEndDate(parseYearMonth(work.getEndTimeYear(), work.getEndTimeMonth()));
            
            // 公司信息
            workHistory.setCompanyIndustry(work.getIndustry());
            workHistory.setCompanyLocation(work.getLocation());
            
            // 转换公司规模
            if (StringUtils.hasText(work.getCompanySize())) {
                workHistory.setCompanySize(convertCompanySize(work.getCompanySize()));
            }
            
            // 薪资信息
            if (StringUtils.hasText(work.getSalary())) {
                BigDecimal[] salaryRange = parseSalaryRange(work.getSalary());
                if (salaryRange != null) {
                    workHistory.setSalaryMin(salaryRange[0]);
                    workHistory.setSalaryMax(salaryRange[1]);
                }
            }
            
            workHistory.setVisibility(DEFAULT_VISIBILITY);
            workHistories.add(workHistory);
        }

        return workHistories;
    }

    /**
     * 转换项目经历
     */
    public List<UserProjectHistory> convertProjectExperience(Long userId, 
            List<ThirdPartyParseResultDTO.ProjectExperience> projectList) {
        if (projectList == null || projectList.isEmpty()) {
            return new ArrayList<>();
        }

        List<UserProjectHistory> projects = new ArrayList<>();
        
        for (ThirdPartyParseResultDTO.ProjectExperience proj : projectList) {
            UserProjectHistory project = new UserProjectHistory();
            project.setUserId(userId);
            project.setProjectName(proj.getProjectName());
            project.setRole(proj.getJobTitle());
            project.setDescription(proj.getDescription());
            project.setAssociatedOrganization(proj.getCompanyName());
            
            // 转换日期
            project.setStartDate(parseYearMonth(proj.getStartTimeYear(), proj.getStartTimeMonth()));
            project.setEndDate(parseYearMonth(proj.getEndTimeYear(), proj.getEndTimeMonth()));
            
            project.setVisibility(DEFAULT_VISIBILITY);
            projects.add(project);
        }

        return projects;
    }

    /**
     * 转换培训经历
     */
    public List<UserTraining> convertTrainingExperience(Long userId, 
            List<ThirdPartyParseResultDTO.TrainingExperience> trainingList) {
        if (trainingList == null || trainingList.isEmpty()) {
            return new ArrayList<>();
        }

        List<UserTraining> trainings = new ArrayList<>();
        
        for (ThirdPartyParseResultDTO.TrainingExperience train : trainingList) {
            UserTraining training = new UserTraining();
            training.setUserId(userId);
            training.setTrainingName(train.getSubject());
            training.setTrainingProvider(train.getOrganizationName());
            training.setDescription(train.getDescription());
            
            // 转换日期
            training.setStartDate(parseYearMonth(train.getStartTimeYear(), train.getStartTimeMonth()));
            training.setEndDate(parseYearMonth(train.getEndTimeYear(), train.getEndTimeMonth()));
            
            training.setVisibility(DEFAULT_VISIBILITY);
            trainings.add(training);
        }

        return trainings;
    }

    /**
     * 转换技能信息
     */
    public List<UserSkills> convertSkills(Long userId, ThirdPartyParseResultDTO.Others others) {
        if (others == null) {
            return new ArrayList<>();
        }

        List<UserSkills> skills = new ArrayList<>();
        
        // IT技能
        if (others.getItSkills() != null) {
            for (String skill : others.getItSkills()) {
                if (StringUtils.hasText(skill)) {
                    UserSkills userSkill = new UserSkills();
                    userSkill.setUserId(userId);
                    userSkill.setSkillName(skill.trim());
                    userSkill.setSkillType(1); // IT技能
                    userSkill.setSource(DATA_SOURCE_RESUME);
                    userSkill.setVisibility(DEFAULT_VISIBILITY);
                    skills.add(userSkill);
                }
            }
        }
        
        // 业务技能
        if (others.getBusinessSkills() != null) {
            for (String skill : others.getBusinessSkills()) {
                if (StringUtils.hasText(skill)) {
                    UserSkills userSkill = new UserSkills();
                    userSkill.setUserId(userId);
                    userSkill.setSkillName(skill.trim());
                    userSkill.setSkillType(2); // 业务技能
                    userSkill.setSource(DATA_SOURCE_RESUME);
                    userSkill.setVisibility(DEFAULT_VISIBILITY);
                    skills.add(userSkill);
                }
            }
        }
        
        // 通用技能
        if (others.getSkills() != null) {
            for (String skill : others.getSkills()) {
                if (StringUtils.hasText(skill)) {
                    // 避免重复添加已经在IT技能或业务技能中的技能
                    boolean isDuplicate = skills.stream()
                            .anyMatch(s -> s.getSkillName().equalsIgnoreCase(skill.trim()));
                    
                    if (!isDuplicate) {
                        UserSkills userSkill = new UserSkills();
                        userSkill.setUserId(userId);
                        userSkill.setSkillName(skill.trim());
                        userSkill.setSkillType(4); // 其他技能
                        userSkill.setSource(DATA_SOURCE_RESUME);
                        userSkill.setVisibility(DEFAULT_VISIBILITY);
                        skills.add(userSkill);
                    }
                }
            }
        }

        return skills;
    }

    // 辅助方法

    private Integer convertGender(String gender) {
        if (gender == null) return 0;
        switch (gender.trim()) {
            case "男": return 1;
            case "女": return 2;
            default: return 0;
        }
    }

    private LocalDate parseDate(String dateStr) {
        if (!StringUtils.hasText(dateStr)) return null;
        
        try {
            // 尝试多种日期格式
            String[] patterns = {"yyyy-MM-dd", "yyyy/MM/dd", "yyyy年MM月dd日", "yyyy-MM", "yyyy/MM"};
            for (String pattern : patterns) {
                try {
                    return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(pattern));
                } catch (DateTimeParseException ignored) {
                }
            }
        } catch (Exception e) {
            log.warn("无法解析日期: {}", dateStr);
        }
        return null;
    }

    private LocalDate parseYearMonth(String year, String month) {
        if (!StringUtils.hasText(year)) return null;
        
        try {
            int y = Integer.parseInt(year);
            int m = StringUtils.hasText(month) ? Integer.parseInt(month) : 1;
            return LocalDate.of(y, m, 1);
        } catch (Exception e) {
            log.warn("无法解析年月: year={}, month={}", year, month);
            return null;
        }
    }

    private Integer convertDegreeLevel(String degree) {
        if (!StringUtils.hasText(degree)) return null;
        
        String d = degree.toLowerCase();
        if (d.contains("博士") || d.contains("phd")) return 4;
        if (d.contains("硕士") || d.contains("master")) return 3;
        if (d.contains("本科") || d.contains("学士") || d.contains("bachelor")) return 2;
        if (d.contains("专科") || d.contains("大专")) return 1;
        if (d.contains("高中") || d.contains("中专")) return 0;
        
        return null;
    }

    private Integer convertCompanySize(String sizeStr) {
        if (!StringUtils.hasText(sizeStr)) return null;
        
        // 提取数字范围
        Pattern pattern = Pattern.compile("(\\d+)-(\\d+)");
        Matcher matcher = pattern.matcher(sizeStr);
        if (matcher.find()) {
            try {
                int max = Integer.parseInt(matcher.group(2));
                if (max < 50) return 1;
                if (max < 200) return 2;
                if (max < 1000) return 3;
                if (max < 5000) return 4;
                return 5;
            } catch (NumberFormatException e) {
                log.warn("无法解析公司规模: {}", sizeStr);
            }
        }
        
        return null;
    }

    private BigDecimal[] parseSalaryRange(String salaryStr) {
        if (!StringUtils.hasText(salaryStr)) return null;
        
        try {
            // 提取数字范围，如 "4001~6000元/月"
            Pattern pattern = Pattern.compile("(\\d+)\\s*[~-]\\s*(\\d+)");
            Matcher matcher = pattern.matcher(salaryStr);
            if (matcher.find()) {
                BigDecimal min = new BigDecimal(matcher.group(1));
                BigDecimal max = new BigDecimal(matcher.group(2));
                return new BigDecimal[]{min, max};
            }
        } catch (Exception e) {
            log.warn("无法解析薪资范围: {}", salaryStr);
        }
        
        return null;
    }
}
