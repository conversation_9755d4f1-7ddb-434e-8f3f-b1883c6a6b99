package com.tinyzk.user.center.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tinyzk.user.center.common.exception.BusinessException;
import com.tinyzk.user.center.dto.ResumeParseRequestDTO;
import com.tinyzk.user.center.dto.ThirdPartyParseResultDTO;
import com.tinyzk.user.center.entity.*;
import com.tinyzk.user.center.mapper.*;
import com.tinyzk.user.center.vo.ResumeParseResultVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 简历解析服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ResumeParseService {

    private final ThirdPartyResumeParseService thirdPartyService;
    private final ResumeDataConversionService conversionService;
    private final ResumeParseRecordsMapper parseRecordsMapper;
    private final UserProfileMapper userProfileMapper;
    private final UserContactMethodsMapper contactMethodsMapper;
    private final UserEducationHistoryMapper educationMapper;
    private final UserWorkHistoryMapper workHistoryMapper;
    private final UserProjectHistoryMapper projectHistoryMapper;
    private final UserTrainingMapper trainingMapper;
    private final UserSkillsMapper skillsMapper;
    private final ObjectMapper objectMapper;

    /**
     * 解析简历
     */
    @Transactional(rollbackFor = Exception.class)
    public ResumeParseResultVO parseResume(Long userId, ResumeParseRequestDTO requestDTO) {
        long startTime = System.currentTimeMillis();
        
        // 创建解析记录
        ResumeParseRecords record = createParseRecord(userId, requestDTO);
        parseRecordsMapper.insert(record);
        
        try {
            // 更新状态为解析中
            record.setParseStatus(1);
            parseRecordsMapper.updateById(record);
            
            // 调用第三方服务解析
            ThirdPartyParseResultDTO parseResult = thirdPartyService.parseResumeWithRetry(requestDTO.getFile());
            
            // 保存完整解析结果
            record.setParseResult(objectMapper.writeValueAsString(parseResult));
            record.setThirdPartyId(parseResult.getCvId());
            
            // 转换并存储数据
            ResumeParseResultVO.ParseStatistics statistics = convertAndSaveData(userId, parseResult, requestDTO);
            
            // 更新解析状态为成功
            long duration = System.currentTimeMillis() - startTime;
            record.setParseStatus(2);
            record.setParseDuration((int) duration);
            parseRecordsMapper.updateById(record);
            
            // 构建返回结果
            return buildResultVO(record, statistics);
            
        } catch (Exception e) {
            // 更新解析状态为失败
            long duration = System.currentTimeMillis() - startTime;
            record.setParseStatus(3);
            record.setErrorMessage(e.getMessage());
            record.setParseDuration((int) duration);
            parseRecordsMapper.updateById(record);
            
            log.error("简历解析失败: userId={}, recordId={}, error={}", 
                    userId, record.getRecordId(), e.getMessage(), e);
            throw new BusinessException("简历解析失败: " + e.getMessage());
        }
    }

    /**
     * 获取解析记录列表
     */
    public List<ResumeParseResultVO> getParseRecords(Long userId) {
        List<ResumeParseRecords> records = parseRecordsMapper.selectByUserId(userId);
        List<ResumeParseResultVO> results = new ArrayList<>();
        
        for (ResumeParseRecords record : records) {
            results.add(buildResultVO(record, null));
        }
        
        return results;
    }

    /**
     * 获取特定解析记录
     */
    public ResumeParseResultVO getParseRecord(Long userId, Long recordId) {
        ResumeParseRecords record = parseRecordsMapper.selectByUserIdAndRecordId(userId, recordId);
        if (record == null) {
            throw new BusinessException("解析记录不存在");
        }
        
        return buildResultVO(record, null);
    }

    /**
     * 创建解析记录
     */
    private ResumeParseRecords createParseRecord(Long userId, ResumeParseRequestDTO requestDTO) {
        ResumeParseRecords record = new ResumeParseRecords();
        record.setUserId(userId);
        record.setOriginalFilename(requestDTO.getFile().getOriginalFilename());
        record.setFileSize(requestDTO.getFile().getSize());
        record.setFileType(getFileExtension(requestDTO.getFile().getOriginalFilename()));
        record.setParseStatus(0); // 待解析
        
        // 计算文件哈希
        try {
            String fileHash = calculateFileHash(requestDTO.getFile().getBytes());
            record.setFileHash(fileHash);
        } catch (Exception e) {
            log.warn("计算文件哈希失败: {}", e.getMessage());
        }
        
        return record;
    }

    /**
     * 转换并存储数据
     */
    private ResumeParseResultVO.ParseStatistics convertAndSaveData(Long userId, 
            ThirdPartyParseResultDTO parseResult, ResumeParseRequestDTO requestDTO) {
        
        ResumeParseResultVO.ParseStatistics statistics = new ResumeParseResultVO.ParseStatistics();
        statistics.setBasicInfoUpdated(false);
        statistics.setContactMethodsAdded(0);
        statistics.setEducationRecordsAdded(0);
        statistics.setWorkExperienceAdded(0);
        statistics.setProjectExperienceAdded(0);
        statistics.setSkillsAdded(0);
        statistics.setTrainingRecordsAdded(0);
        statistics.setLanguagesAdded(0);
        statistics.setCertificatesAdded(0);
        statistics.setAwardsAdded(0);
        statistics.setDuplicateRecordsSkipped(0);
        statistics.setFailedRecords(0);
        statistics.setFailureDetails(new ArrayList<>());
        
        ThirdPartyParseResultDTO.ParsingResult result = parseResult.getParsingResult();
        if (result == null) {
            return statistics;
        }

        try {
            // 转换并保存基本信息
            if (requestDTO.getParseBasicInfo() && result.getBasicInfo() != null) {
                UserProfile profile = conversionService.convertBasicInfo(userId, result.getBasicInfo());
                if (profile != null) {
                    // 检查是否已存在用户资料
                    UserProfile existingProfile = userProfileMapper.selectByUserId(userId);
                    if (existingProfile != null && !requestDTO.getOverwriteExisting()) {
                        log.info("用户资料已存在，跳过更新: userId={}", userId);
                    } else {
                        if (existingProfile != null) {
                            profile.setProfileId(existingProfile.getProfileId());
                            userProfileMapper.updateById(profile);
                        } else {
                            userProfileMapper.insert(profile);
                        }
                        statistics.setBasicInfoUpdated(true);
                    }
                }
            }

            // 转换并保存联系方式
            if (requestDTO.getParseContactInfo() && result.getContactInfo() != null) {
                List<UserContactMethods> contacts = conversionService.convertContactInfo(userId, result.getContactInfo());
                for (UserContactMethods contact : contacts) {
                    // 检查是否已存在相同联系方式
                    if (!isDuplicateContact(userId, contact)) {
                        contactMethodsMapper.insert(contact);
                        statistics.setContactMethodsAdded(statistics.getContactMethodsAdded() + 1);
                    } else {
                        statistics.setDuplicateRecordsSkipped(statistics.getDuplicateRecordsSkipped() + 1);
                    }
                }
            }

            // 转换并保存教育经历
            if (requestDTO.getParseEducation() && result.getEducationExperience() != null) {
                List<UserEducationHistory> educations = conversionService.convertEducationExperience(userId, result.getEducationExperience());
                for (UserEducationHistory education : educations) {
                    if (!isDuplicateEducation(userId, education)) {
                        educationMapper.insert(education);
                        statistics.setEducationRecordsAdded(statistics.getEducationRecordsAdded() + 1);
                    } else {
                        statistics.setDuplicateRecordsSkipped(statistics.getDuplicateRecordsSkipped() + 1);
                    }
                }
            }

            // 转换并保存工作经历
            if (requestDTO.getParseWorkExperience() && result.getWorkExperience() != null) {
                List<UserWorkHistory> workHistories = conversionService.convertWorkExperience(userId, result.getWorkExperience());
                for (UserWorkHistory workHistory : workHistories) {
                    if (!isDuplicateWorkHistory(userId, workHistory)) {
                        workHistoryMapper.insert(workHistory);
                        statistics.setWorkExperienceAdded(statistics.getWorkExperienceAdded() + 1);
                    } else {
                        statistics.setDuplicateRecordsSkipped(statistics.getDuplicateRecordsSkipped() + 1);
                    }
                }
            }

            // 转换并保存项目经历
            if (requestDTO.getParseProjectExperience() && result.getProjectExperience() != null) {
                List<UserProjectHistory> projects = conversionService.convertProjectExperience(userId, result.getProjectExperience());
                for (UserProjectHistory project : projects) {
                    if (!isDuplicateProject(userId, project)) {
                        projectHistoryMapper.insert(project);
                        statistics.setProjectExperienceAdded(statistics.getProjectExperienceAdded() + 1);
                    } else {
                        statistics.setDuplicateRecordsSkipped(statistics.getDuplicateRecordsSkipped() + 1);
                    }
                }
            }

            // 转换并保存培训经历
            if (requestDTO.getParseTraining() && result.getTrainingExperience() != null) {
                List<UserTraining> trainings = conversionService.convertTrainingExperience(userId, result.getTrainingExperience());
                for (UserTraining training : trainings) {
                    if (!isDuplicateTraining(userId, training)) {
                        trainingMapper.insert(training);
                        statistics.setTrainingRecordsAdded(statistics.getTrainingRecordsAdded() + 1);
                    } else {
                        statistics.setDuplicateRecordsSkipped(statistics.getDuplicateRecordsSkipped() + 1);
                    }
                }
            }

            // 转换并保存技能
            if (requestDTO.getParseSkills() && result.getOthers() != null) {
                List<UserSkills> skills = conversionService.convertSkills(userId, result.getOthers());
                for (UserSkills skill : skills) {
                    if (!isDuplicateSkill(userId, skill)) {
                        skillsMapper.insert(skill);
                        statistics.setSkillsAdded(statistics.getSkillsAdded() + 1);
                    } else {
                        statistics.setDuplicateRecordsSkipped(statistics.getDuplicateRecordsSkipped() + 1);
                    }
                }
            }

        } catch (Exception e) {
            log.error("数据转换和存储过程中发生错误: {}", e.getMessage(), e);
            statistics.setFailedRecords(statistics.getFailedRecords() + 1);
            statistics.getFailureDetails().add("数据存储失败: " + e.getMessage());
        }

        return statistics;
    }

    /**
     * 构建返回结果VO
     */
    private ResumeParseResultVO buildResultVO(ResumeParseRecords record, ResumeParseResultVO.ParseStatistics statistics) {
        ResumeParseResultVO vo = new ResumeParseResultVO();
        vo.setRecordId(record.getRecordId());
        vo.setParseStatus(record.getParseStatus());
        vo.setParseStatusDesc(getParseStatusDesc(record.getParseStatus()));
        vo.setOriginalFilename(record.getOriginalFilename());
        vo.setFileSize(record.getFileSize());
        vo.setFileType(record.getFileType());
        vo.setParseDuration(record.getParseDuration());
        vo.setErrorMessage(record.getErrorMessage());
        vo.setCreatedAt(record.getCreatedAt());
        vo.setStatistics(statistics);
        
        return vo;
    }

    // 辅助方法

    private String getFileExtension(String filename) {
        if (filename == null) return "";
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex == -1 ? "" : filename.substring(lastDotIndex + 1).toLowerCase();
    }

    private String calculateFileHash(byte[] fileBytes) {
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(fileBytes);
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            return null;
        }
    }

    private String getParseStatusDesc(Integer status) {
        if (status == null) return "未知";
        switch (status) {
            case 0: return "待解析";
            case 1: return "解析中";
            case 2: return "解析成功";
            case 3: return "解析失败";
            default: return "未知状态";
        }
    }

    // 重复检查方法
    private boolean isDuplicateContact(Long userId, UserContactMethods contact) {
        // 简单实现：检查相同类型和值的联系方式是否已存在
        return contactMethodsMapper.selectByUserIdAndTypeAndValue(userId, 
                contact.getContactType(), contact.getContactValue()) != null;
    }

    private boolean isDuplicateEducation(Long userId, UserEducationHistory education) {
        // 检查相同学校和专业的教育经历是否已存在
        return educationMapper.selectByUserIdAndSchoolAndMajor(userId, 
                education.getSchoolName(), education.getMajor()) != null;
    }

    private boolean isDuplicateWorkHistory(Long userId, UserWorkHistory workHistory) {
        // 检查相同公司和职位的工作经历是否已存在
        return workHistoryMapper.selectByUserIdAndCompanyAndPosition(userId, 
                workHistory.getCompanyName(), workHistory.getPositionName()) != null;
    }

    private boolean isDuplicateProject(Long userId, UserProjectHistory project) {
        // 检查相同项目名称的项目经历是否已存在
        return projectHistoryMapper.selectByUserIdAndProjectName(userId, 
                project.getProjectName()) != null;
    }

    private boolean isDuplicateTraining(Long userId, UserTraining training) {
        // 检查相同培训名称和机构的培训经历是否已存在
        return trainingMapper.selectByUserIdAndNameAndProvider(userId, 
                training.getTrainingName(), training.getTrainingProvider()) != null;
    }

    private boolean isDuplicateSkill(Long userId, UserSkills skill) {
        // 检查相同技能名称是否已存在
        return skillsMapper.selectByUserIdAndSkillName(userId, skill.getSkillName()) != null;
    }
}
