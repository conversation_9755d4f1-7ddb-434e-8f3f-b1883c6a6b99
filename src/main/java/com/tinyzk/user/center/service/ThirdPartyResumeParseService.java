package com.tinyzk.user.center.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tinyzk.user.center.common.exception.BusinessException;
import com.tinyzk.user.center.common.exception.FileUploadException;
import com.tinyzk.user.center.common.exception.ResumeParseException;
import com.tinyzk.user.center.config.ResumeParseConfig;
import com.tinyzk.user.center.dto.ThirdPartyParseResultDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;

/**
 * 第三方简历解析服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ThirdPartyResumeParseService {

    private final ResumeParseConfig config;
    @Qualifier("resumeParseRestTemplate")
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    /**
     * 调用第三方服务解析简历
     *
     * @param file 简历文件
     * @return 解析结果
     */
    public ThirdPartyParseResultDTO parseResume(MultipartFile file) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 验证文件
            validateFile(file);
            
            // 计算文件哈希
            String fileHash = calculateFileHash(file);
            log.info("开始解析简历文件: filename={}, size={}, hash={}", 
                    file.getOriginalFilename(), file.getSize(), fileHash);
            
            // 调用第三方API
            ThirdPartyParseResultDTO result = callThirdPartyApi(file);
            
            // 验证解析结果
            validateParseResult(result);
            
            long duration = System.currentTimeMillis() - startTime;
            log.info("简历解析完成: filename={}, duration={}ms, cvId={}", 
                    file.getOriginalFilename(), duration, result.getCvId());
            
            return result;
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("简历解析失败: filename={}, duration={}ms, error={}", 
                    file.getOriginalFilename(), duration, e.getMessage(), e);
            throw new BusinessException("简历解析失败: " + e.getMessage());
        }
    }

    /**
     * 验证上传的文件
     */
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new FileUploadException("文件不能为空");
        }

        // 检查文件大小
        if (file.getSize() > config.getMaxFileSize()) {
            throw new FileUploadException("文件大小超过限制，最大允许 " +
                    (config.getMaxFileSize() / 1024 / 1024) + "MB");
        }

        // 检查文件类型
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null) {
            throw new FileUploadException("文件名不能为空");
        }

        String fileExtension = getFileExtension(originalFilename).toLowerCase();
        if (!Arrays.asList(config.getSupportedFileTypes()).contains(fileExtension)) {
            throw new FileUploadException("不支持的文件类型，仅支持: " +
                    String.join(", ", config.getSupportedFileTypes()));
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return "";
        }
        return filename.substring(lastDotIndex + 1);
    }

    /**
     * 计算文件MD5哈希值
     */
    private String calculateFileHash(MultipartFile file) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(file.getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException | IOException e) {
            log.warn("计算文件哈希失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 调用第三方API - 使用手动构建multipart请求
     */
    private ThirdPartyParseResultDTO callThirdPartyApi(MultipartFile file) {
        try {
            // 验证文件
            if (file.isEmpty()) {
                throw new ResumeParseException("上传的文件为空");
            }

            log.info("准备调用第三方API: url={}, filename={}, size={}",
                    config.getApiUrl(), file.getOriginalFilename(), file.getSize());

            // 获取文件字节数组
            byte[] fileBytes = file.getBytes();
            log.info("文件字节数组大小: {}", fileBytes.length);

            // 创建文件资源，确保文件名正确传递
            String rawFilename = file.getOriginalFilename();
            final String cleanFilename = (rawFilename != null) ? rawFilename.trim() : "resume.doc";

            // 方案1: 尝试使用ByteArrayResource并指定文件名
            ByteArrayResource fileResource = new ByteArrayResource(fileBytes) {
                @Override
                public String getFilename() {
                    return cleanFilename;
                }
            };

            // 构建请求头 - 不手动设置Content-Type，让Spring自动处理
            HttpHeaders headers = new HttpHeaders();
            // 注意：不设置Content-Type，让Spring自动设置multipart/form-data和boundary

            // 构建请求体
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("file", fileResource);

            // 添加其他参数
            body.add("rawtext", "1");
            body.add("handle_image", "1");
            body.add("avatar", "1");
            body.add("parse_mode", "fast");
            body.add("ocr_mode", "accurate");
            body.add("ocr_service", "OCR");

            log.info("请求参数: file={} ({}字节), rawtext=1, handle_image=1, avatar=1, parse_mode=fast",
                    cleanFilename, fileBytes.length);
            log.info("请求体包含 {} 个参数", body.size());

            // 打印所有请求参数
            for (String key : body.keySet()) {
                Object value = body.getFirst(key);
                if (value instanceof ByteArrayResource) {
                    ByteArrayResource resource = (ByteArrayResource) value;
                    log.info("参数 {}: ByteArrayResource, 文件名={}, 大小={}字节",
                            key, resource.getFilename(), resource.contentLength());
                } else {
                    log.info("参数 {}: {}", key, value);
                }
            }

            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            // 发送请求
            log.info("发送HTTP请求到第三方API...");
            log.info("请求头: {}", headers);

            ResponseEntity<String> response = restTemplate.exchange(
                    config.getApiUrl(),
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            log.info("收到第三方API响应: status={}", response.getStatusCode());

            if (response.getStatusCode() != HttpStatus.OK) {
                throw new ResumeParseException("第三方服务调用失败，状态码: " + response.getStatusCode());
            }

            String responseBody = response.getBody();
            if (responseBody == null) {
                throw new ResumeParseException("第三方服务返回空响应");
            }

            log.info("第三方API响应内容: {}", responseBody);

            // 解析响应
            return objectMapper.readValue(responseBody, ThirdPartyParseResultDTO.class);
        } catch (IOException e) {
            log.error("调用第三方解析服务失败", e);
            throw new ResumeParseException("调用第三方解析服务失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("第三方API调用异常", e);
            throw new ResumeParseException("第三方API调用异常: " + e.getMessage());
        }
    }



    /**
     * 验证解析结果
     */
    private void validateParseResult(ThirdPartyParseResultDTO result) {
        if (result == null) {
            throw new ResumeParseException("解析结果为空");
        }

        if (result.getErrorCode() == null || result.getErrorCode() != 0) {
            String errorMsg = result.getErrorMessage() != null ?
                    result.getErrorMessage() : "未知错误";
            throw new ResumeParseException("解析失败: " + errorMsg);
        }

        if (result.getParsingResult() == null) {
            throw new ResumeParseException("解析结果数据为空");
        }
    }

    /**
     * 重试机制包装
     */
    public ThirdPartyParseResultDTO parseResumeWithRetry(MultipartFile file) {
        int maxRetries = config.getMaxRetries();
        Exception lastException = null;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return parseResume(file);
            } catch (Exception e) {
                lastException = e;
                log.warn("简历解析第 {} 次尝试失败: {}", attempt, e.getMessage());
                
                if (attempt < maxRetries) {
                    try {
                        // 指数退避策略
                        long delay = (long) Math.pow(2, attempt - 1) * 1000;
                        TimeUnit.MILLISECONDS.sleep(delay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new ResumeParseException("解析过程被中断");
                    }
                }
            }
        }

        throw new ResumeParseException("简历解析失败，已重试 " + maxRetries + " 次: " +
                (lastException != null ? lastException.getMessage() : "未知错误"));
    }
}
