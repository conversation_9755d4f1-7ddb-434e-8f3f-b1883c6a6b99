package com.tinyzk.user.center.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 第三方简历解析结果DTO
 */
@Data
public class ThirdPartyParseResultDTO {

    @JsonProperty("errorcode")
    private Integer errorCode;

    @JsonProperty("errormessage")
    private String errorMessage;

    @JsonProperty("cv_id")
    private String cvId;

    @JsonProperty("file_name")
    private String fileName;

    @JsonProperty("parsing_result")
    private ParsingResult parsingResult;

    @Data
    public static class ParsingResult {
        
        @JsonProperty("basic_info")
        private BasicInfo basicInfo;
        
        @JsonProperty("contact_info")
        private ContactInfo contactInfo;
        
        @JsonProperty("education_experience")
        private List<EducationExperience> educationExperience;
        
        @JsonProperty("work_experience")
        private List<WorkExperience> workExperience;
        
        @JsonProperty("project_experience")
        private List<ProjectExperience> projectExperience;
        
        @JsonProperty("training_experience")
        private List<TrainingExperience> trainingExperience;
        
        @JsonProperty("others")
        private Others others;
    }

    @Data
    public static class BasicInfo {
        private String name;
        private String gender;
        private Integer age;
        @JsonProperty("date_of_birth")
        private String dateOfBirth;
        private String degree;
        @JsonProperty("current_location")
        private String currentLocation;
        @JsonProperty("detailed_location")
        private String detailedLocation;
        @JsonProperty("current_company")
        private String currentCompany;
        @JsonProperty("current_position")
        private String currentPosition;
        @JsonProperty("current_salary")
        private String currentSalary;
        @JsonProperty("desired_salary")
        private String desiredSalary;
        @JsonProperty("desired_position")
        private String desiredPosition;
        @JsonProperty("desired_industry")
        private String desiredIndustry;
        @JsonProperty("expect_location")
        private String expectLocation;
        @JsonProperty("marital_status")
        private String maritalStatus;
        @JsonProperty("political_status")
        private String politicalStatus;
        private String ethnic;
        private String birthplace;
        @JsonProperty("num_work_experience")
        private Integer numWorkExperience;
        @JsonProperty("work_start_year")
        private String workStartYear;
        @JsonProperty("school_name")
        private String schoolName;
        private String major;
        private String industry;
    }

    @Data
    public static class ContactInfo {
        @JsonProperty("phone_number")
        private String phoneNumber;
        @JsonProperty("home_phone_number")
        private String homePhoneNumber;
        private String email;
        private String wechat;
        @JsonProperty("QQ")
        private String qq;
    }

    @Data
    public static class EducationExperience {
        @JsonProperty("school_name")
        private String schoolName;
        private String degree;
        private String major;
        private String department;
        @JsonProperty("start_time_year")
        private String startTimeYear;
        @JsonProperty("start_time_month")
        private String startTimeMonth;
        @JsonProperty("end_time_year")
        private String endTimeYear;
        @JsonProperty("end_time_month")
        private String endTimeMonth;
        @JsonProperty("still_active")
        private Integer stillActive;
        private String location;
        @JsonProperty("GPA")
        private String gpa;
        private String courses;
        private String ranking;
        @JsonProperty("school_level")
        private String schoolLevel;
        @JsonProperty("school_rank")
        private String schoolRank;
        @JsonProperty("study_model")
        private String studyModel;
        private Integer abroad;
        @JsonProperty("abroad_country")
        private String abroadCountry;
    }

    @Data
    public static class WorkExperience {
        @JsonProperty("company_name")
        private String companyName;
        @JsonProperty("job_title")
        private String jobTitle;
        private String department;
        @JsonProperty("start_time_year")
        private String startTimeYear;
        @JsonProperty("start_time_month")
        private String startTimeMonth;
        @JsonProperty("end_time_year")
        private String endTimeYear;
        @JsonProperty("end_time_month")
        private String endTimeMonth;
        @JsonProperty("still_active")
        private Integer stillActive;
        private String description;
        private String salary;
        private String location;
        @JsonProperty("job_function")
        private String jobFunction;
        @JsonProperty("company_type")
        private String companyType;
        @JsonProperty("company_size")
        private String companySize;
        private String industry;
        @JsonProperty("report_to")
        private String reportTo;
        @JsonProperty("underling_num")
        private String underlingNum;
        private List<String> skills;
    }

    @Data
    public static class ProjectExperience {
        @JsonProperty("project_name")
        private String projectName;
        @JsonProperty("company_name")
        private String companyName;
        @JsonProperty("job_title")
        private String jobTitle;
        @JsonProperty("job_function")
        private String jobFunction;
        @JsonProperty("start_time_year")
        private String startTimeYear;
        @JsonProperty("start_time_month")
        private String startTimeMonth;
        @JsonProperty("end_time_year")
        private String endTimeYear;
        @JsonProperty("end_time_month")
        private String endTimeMonth;
        @JsonProperty("still_active")
        private Integer stillActive;
        private String description;
        private String location;
        private List<String> skills;
    }

    @Data
    public static class TrainingExperience {
        @JsonProperty("organization_name")
        private String organizationName;
        private String subject;
        @JsonProperty("start_time_year")
        private String startTimeYear;
        @JsonProperty("start_time_month")
        private String startTimeMonth;
        @JsonProperty("end_time_year")
        private String endTimeYear;
        @JsonProperty("end_time_month")
        private String endTimeMonth;
        @JsonProperty("still_active")
        private Integer stillActive;
        private String description;
        private String location;
    }

    @Data
    public static class Others {
        private List<String> language;
        private List<String> certificate;
        private List<String> skills;
        private List<String> awards;
        @JsonProperty("it_skills")
        private List<String> itSkills;
        @JsonProperty("business_skills")
        private List<String> businessSkills;
        @JsonProperty("self_evaluation")
        private String selfEvaluation;
    }
}
