## 服务配置
spring:
  application:
    name: user-center
  profiles:
    active: local
  aop:
    proxy-target-class: true
    
  main:
    allow-bean-definition-overriding: true
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  cloud:
    compatibility-verifier:
      enabled: false
    nacos:
      config:
        import-check:
          enabled: false
      discovery:
        enabled: false

management:
  server:
    port: 18080
  health:
    elasticsearch:
      enabled: false
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always
    prometheus:
      enabled: true
  prometheus:
    metrics:
      export:
        enabled: true
  metrics:
    distribution:
      percentiles-histogram:
        "[http.server.requests]": true
      percentiles:
        "[http.server.requests]": 0.5, 0.95, 0.99
    tags:
      application: ${spring.application.name}
  tracing:
    sampling:
      probability: 0.1
  # SkyWalking 配置通过 Agent 参数设置，无需在配置文件中配置

# 缓存预热配置
cache:
  warmup:
    enabled: true
    startup-warmup: true
    schedule:
      enabled: true
      cron: "0 0 2 * * ?"
      before-expire: PT2M
    strategy:
      user-detail-count: 1000
      user-list-pages: 5
      page-size: 20
      hot-data-days: 7
      priorities:
        - userDetail
        - userList
        - clientDetails
        - userMapping
    resource:
      thread-pool-size: 5
      timeout: PT30M
      batch-size: 100
      interval: PT0.1S

# 简历解析配置
resume:
  parse:
    api-url: http://192.168.0.124:8000/parse_file
    timeout: 30000
    max-retries: 3
    supported-file-types:
      - doc
      - docx
      - pdf
    max-file-size: 10485760  # 10MB
    enable-file-cache: true
    file-cache-dir: /tmp/resume-cache
    enable-result-cache: true
    result-cache-expire: 3600